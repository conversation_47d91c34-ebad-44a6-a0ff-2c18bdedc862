<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.2</version>
        <relativePath/><!-- lookup parent from repository -->
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>com.org</groupId>
    <artifactId>payment-transaction-history</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>payment-transaction-history</name>
    <description>payment-transaction-history</description>

    <modules>
        <module>pth-core</module>
        <!--<module>upi-data-adaptor</module>
        <module>wallet-data-adaptor</module>-->
        <module>pth-rule-engine</module>
        <module>flink-ingestion-service</module>
        <module>pth-web</module>
        <module>pth-commons</module>
        <module>pth-data-group</module>
        <module>pth-service</module>
        <module>pth-cst</module>
        <module>cache</module>
        <module>rate-limit</module>

    </modules>

    <properties>
        <java.version>17</java.version>
        <!--Need to change below flink version for aws-->
        <flink.version>1.18.1</flink.version>
        <lombok.version>1.18.22</lombok.version>
        <junit.version>4.12</junit.version>
        <pb.commons.version>1.2.58-SNAPSHOT</pb.commons.version>
        <lmax.version>3.3.7</lmax.version>
        <spring-fox.swagger.version>3.0.0-SNAPSHOT</spring-fox.swagger.version>
        <jackson-fasterxml.version>2.10.0</jackson-fasterxml.version>
        <jackson.version>2.10.0</jackson.version>
        <spring-data.elasticsearch.version>3.2.13.RELEASE</spring-data.elasticsearch.version>
        <elasticsearch.version>6.8.4</elasticsearch.version>
        <beanutils.version>1.9.3</beanutils.version>
        <reactor-kafka.version>1.1.0.RELEASE</reactor-kafka.version>
        <avro.version>1.9.1</avro.version>
        <confluent-kafka.avro.serializer.version>5.3.2</confluent-kafka.avro.serializer.version>
        <tech.allegro.json2avro.converter.version>0.2.9</tech.allegro.json2avro.converter.version>
        <aerospike-client.version>4.5.0</aerospike-client.version>
        <git.commit.plugin.version>2.2.4</git.commit.plugin.version>
        <mavan.compiler.plugin.version>3.10.1</mavan.compiler.plugin.version>
        <maven.shade.plugin.version>3.2.4</maven.shade.plugin.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <gson.version>2.8.6</gson.version>
        <datadog-client.version>2.3</datadog-client.version>
        <sonar.maven.plugin.version>3.9.1.2184</sonar.maven.plugin.version>
        <!--<<put your sonal host url here>>-->
        <sonar.host.url>https://sonarqube.orgb.com</sonar.host.url>
        <sonar.sources>src/main</sonar.sources>
        <springdoc.version>1.6.13</springdoc.version>
        <log4j.version>2.23.1</log4j.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <aerospike.cache.version>3.4.0</aerospike.cache.version>
        <!--aerospike.client.version>4.4.6</aerospike.client.version>-->
        <!-- Latest Spring Java Format Version -->
        <spring-javaformat.version>0.0.44</spring-javaformat.version>
        <checkstyle.skip>true</checkstyle.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>pth-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>flink-ingestion-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aerospike</groupId>
                <artifactId>spring-data-aerospike</artifactId>
                <version>${aerospike.cache.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aerospike</groupId>
                        <artifactId>aerospike-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--<dependency>
                <groupId>com.org</groupId>
                <artifactId>upi-data-adaptor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>wallet-data-adaptor</artifactId>
                <version>${project.version}</version>
            </dependency>-->
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>pth-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>pth-commons</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>pth-rule-engine</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.org</groupId>
                <artifactId>pth-data-group</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.tomakehurst</groupId>
                <artifactId>wiremock-jre8</artifactId>
                <version>2.35.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka_2.11</artifactId>
                <version>1.14.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-avro</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-elasticsearch6_2.11</artifactId>
                <version>1.12.1</version>
            </dependency>
            <!--<dependency>
                <groupId>com.paytm.bank</groupId>
                <artifactId>commons</artifactId>
                <version>${pb.commons.version}</version>
            </dependency>-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>6.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${lmax.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-fasterxml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-elasticsearch</artifactId>
                <version>${spring-data.elasticsearch.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>io.projectreactor.kafka</groupId>
                <artifactId>reactor-kafka</artifactId>
                <version>${reactor-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-avro-serializer</artifactId>
                <version>${confluent-kafka.avro.serializer.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>
            <dependency>
                <groupId>tech.allegro.schema.json2avro</groupId>
                <artifactId>converter</artifactId>
                <version>${tech.allegro.json2avro.converter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aerospike</groupId>
                <artifactId>aerospike-client</artifactId>
                <version>${aerospike-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datadoghq</groupId>
                <artifactId>java-dogstatsd-client</artifactId>
                <version>${datadog-client.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.10.5</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>1.10.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>2.5.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--not adding dependency : spring-boot-actuator-autoconfigure in pth-service as pth-service inherits pth-web-api-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                <version>3.0.11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>1.5.0</version>
                <scope>test</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <!--<dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>6.0.1</version>
            </dependency>-->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>2.2.220</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.luben</groupId>
                <artifactId>zstd-jni</artifactId>
                <version>1.5.5-11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-test -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>3.0.2</version>
                <scope>test</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>add-dependencies-for-IDEA</id>

            <activation>
                <property>
                    <name>idea.version</name>
                </property>
            </activation>

            <dependencyManagement>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.flink</groupId>
                        <artifactId>flink-java</artifactId>
                        <version>${flink.version}</version>
                        <!--                        <scope>compile</scope>-->
                    </dependency>
                    <dependency>
                        <groupId>org.apache.flink</groupId>
                        <artifactId>flink-streaming-java</artifactId>
                        <version>${flink.version}</version>
                        <!--                        version<scope>compile</scope>-->
                    </dependency>
                </dependencies>
            </dependencyManagement>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-surefire-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.4</version>
                <configuration>
                    <argLine>-Dspring.profiles.active=local --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.util.concurrent=ALL-UNNAMED --add-opens java.desktop/java.awt=ALL-UNNAMED --add-opens java.desktop/java.awt.geom=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED</argLine>
                </configuration>
            </plugin>

            <!-- Spring Java Format Plugin -->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring-javaformat.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <inherited>true</inherited>
                        <goals>
                            <goal>validate</goal> <!-- Validates formatting -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Checkstyle with Spring Rules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.6.0</version>
                <configuration>
                    <configLocation>io/spring/javaformat/checkstyle/checkstyle.xml</configLocation>
                    <failOnViolation>true</failOnViolation>
                    <consoleOutput>true</consoleOutput>
                    <violationSeverity>error</violationSeverity>
                    <failsOnError>true</failsOnError>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>9.3</version>
                    </dependency>
                    <dependency>
                        <groupId>io.spring.javaformat</groupId>
                        <artifactId>spring-javaformat-checkstyle</artifactId>
                        <version>${spring-javaformat.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>checkstyle-validation</id>
                        <phase>validate</phase>
                        <inherited>true</inherited>
                        <configuration>
                            <configLocation>io/spring/javaformat/checkstyle/checkstyle.xml</configLocation>
                            <includeTestSourceDirectory>true</includeTestSourceDirectory>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- Java Compiler -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${mavan.compiler.plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar.maven.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git.commit.plugin.version}</version>
                </plugin>

                <!-- Checkstyle with Spring Rules -->
                <!-- We use the maven-shade plugin to create a fat jar that contains all necessary dependencies. -->
                <!-- Change the value of <mainClass>...</mainClass> if your program entry point changes. -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.plugin.version}</version>
                    <!--<executions>-->
                    <!--&lt;!&ndash; Run shade goal on package phase &ndash;&gt;-->
                    <!--<execution>-->
                    <!--&lt;!&ndash;<id>bank-shade-jar</id>&ndash;&gt;-->
                    <!--<phase>package</phase>-->
                    <!--<goals>-->
                    <!--<goal>shade</goal>-->
                    <!--</goals>-->
                    <!--<configuration>-->
                    <!--<artifactSet>-->
                    <!--<excludes>-->
                    <!--<exclude>org.apache.flink:force-shading</exclude>-->
                    <!--<exclude>com.google.code.findbugs:jsr305</exclude>-->
                    <!--&lt;!&ndash;<exclude>org.slf4j:*</exclude>&ndash;&gt;-->
                    <!--&lt;!&ndash;<exclude>log4j:*</exclude>&ndash;&gt;-->
                    <!--</excludes>-->
                    <!--</artifactSet>-->
                    <!--<filters>-->
                    <!--<filter>-->
                    <!--&lt;!&ndash; Do not copy the signatures in the META-INF folder.-->
                    <!--Otherwise, this might cause SecurityExceptions when using the JAR. &ndash;&gt;-->
                    <!--<artifact>*:*</artifact>-->
                    <!--<excludes>-->
                    <!--<exclude>META-INF/*.SF</exclude>-->
                    <!--<exclude>META-INF/*.DSA</exclude>-->
                    <!--<exclude>META-INF/*.RSA</exclude>-->
                    <!--</excludes>-->
                    <!--</filter>-->
                    <!--</filters>-->
                    <!--<transformers>-->
                    <!--<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">-->
                    <!--<manifestEntries>-->
                    <!--<mainClass>{start-class}</mainClass>-->
                    <!--</manifestEntries>-->
                    <!--</transformer>-->
                    <!--</transformers>-->
                    <!--</configuration>-->
                    <!--</execution>-->
                    <!--</executions>-->
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!--    <distributionManagement>-->
    <!--        <repository>-->
    <!--            <id>central</id>-->
    <!--            &lt;!&ndash;organisation actifactory&ndash;&gt;-->
    <!--            <name>artifactory.orgb.io-releases</name>-->
    <!--            &lt;!&ndash;organisation actifactory url&ndash;&gt;-->
    <!--            <url>https://artifactory.orgb.io/artifactory/libs-release</url>-->
    <!--        </repository>-->
    <!--        <snapshotRepository>-->
    <!--            <id>snapshots</id>-->
    <!--            &lt;!&ndash;organisation actifactory&ndash;&gt;-->
    <!--            <name>artifactory.orgb.io-snapshots</name>-->
    <!--            &lt;!&ndash;organisation actifactory url&ndash;&gt;-->
    <!--            <url>https://artifactory.orgb.io/artifactory/libs-snapshot</url>-->
    <!--        </snapshotRepository>-->
    <!--    </distributionManagement>-->

</project>
